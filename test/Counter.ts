import { expect } from "chai";
import { describe, it } from "node:test";

import { network } from "hardhat";

describe("Counter", async () => {
  const { ethers } = await network.connect({
    network: "hardhatMainnet",
    chainType: "l1",
  });

  it("should increment the counter", async () => {
    const counter = await ethers.deployContract("Counter");
    await counter.increment(5);
    expect(await counter.get()).to.equal(5);
  });
});
