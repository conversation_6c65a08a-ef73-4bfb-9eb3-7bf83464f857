// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract Todo {
    enum Status {
        Pending,
        Processing,
        Completed,
        Canceled
    }

    struct Task {
        Status status;
        string text;
        uint timestamp;
    }
    Task[] public todos;

    constructor() {}

    function createTask(string calldata _text) external {
        Task memory task = Task({
            status: Status.Pending,
            text: _text,
            timestamp: block.timestamp
        });
        todos.push(task);
    }

    function removeTask(uint idx) external {
        uint taskNum = todos.length;
        require(idx > taskNum - 1, "idx not found");
        todos[idx] = todos[taskNum - 1];
        todos.pop();
    }
}
