// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract Todo {
    enum Status {
        Pending,
        Processing,
        Completed,
        Canceled
    }

    struct Task {
        Status status;
        string text;
        uint timestamp;
        address creator; // 添加创建者地址
    }
    Task[] public todos;

    // 事件
    event TaskCreated(
        uint indexed taskId,
        address indexed creator,
        string text
    );
    event TaskRemoved(uint indexed taskId, address indexed remover);
    event TaskStatusUpdated(uint indexed taskId, Status newStatus);
    event TaskTextUpdated(uint indexed taskId, string newText);

    constructor() {}

    function createTask(string calldata _text) external {
        require(bytes(_text).length > 0, "Task text cannot be empty");

        Task memory task = Task({
            status: Status.Pending,
            text: _text,
            timestamp: block.timestamp,
            creator: msg.sender
        });

        uint taskId = todos.length;
        todos.push(task);

        emit TaskCreated(taskId, msg.sender, _text);
    }

    function removeTask(uint idx) external {
        uint taskNum = todos.length;
        require(idx < taskNum, "Task index out of bounds");

        emit TaskRemoved(idx, msg.sender);

        // 将最后一个元素移到要删除的位置
        todos[idx] = todos[taskNum - 1];
        todos.pop();
    }

    // 更新任务状态
    function updateTaskStatus(uint idx, Status _status) external {
        require(idx < todos.length, "Task index out of bounds");
        todos[idx].status = _status;
        emit TaskStatusUpdated(idx, _status);
    }

    // 更新任务文本
    function updateTaskText(uint idx, string calldata _text) external {
        require(idx < todos.length, "Task index out of bounds");
        require(bytes(_text).length > 0, "Task text cannot be empty");
        todos[idx].text = _text;
        emit TaskTextUpdated(idx, _text);
    }

    // 获取任务总数
    function getTaskCount() external view returns (uint) {
        return todos.length;
    }

    // 获取所有任务（注意：大量任务时可能消耗大量 gas）
    function getAllTasks() external view returns (Task[] memory) {
        return todos;
    }
}
