{"_format": "hh3-sol-build-info-output-1", "id": "solc-0_8_28-fb83ed9611c4c1fe2bb8b31a5847b4899eb763ab", "output": {"contracts": {"project/contracts/Counter.sol": {"Counter": {"abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "num", "type": "uint256"}], "name": "minusNumLog", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "num", "type": "uint256"}], "name": "plusNumLog", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "y", "type": "uint256"}], "name": "decrement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "get", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "y", "type": "uint256"}], "name": "increment", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "x", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "6080604052348015600e575f5ffd5b506103c08061001c5f395ff3fe608060405234801561000f575f5ffd5b506004361061004a575f3560e01c80630c55699c1461004e5780633a9ebefd1461006c5780636d4ce63c146100885780637cf5dab0146100a6575b5f5ffd5b6100566100c2565b604051610063919061020d565b60405180910390f35b61008660048036038101906100819190610254565b6100c7565b005b61009061015a565b60405161009d919061020d565b60405180910390f35b6100c060048036038101906100bb9190610254565b610162565b005b5f5481565b5f8111610109576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610100906102d9565b60405180910390fd5b805f5f8282546101199190610324565b925050819055507f4c5d8c5a3755953e6d51fbc8a808109f0efb2039c64de8d21fea4811a0126bf28160405161014f919061020d565b60405180910390a150565b5f5f54905090565b5f81116101a4576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161019b906102d9565b60405180910390fd5b805f5f8282546101b49190610357565b925050819055507f0f15468f8ed097936d939a4f3fcd54599dad6662b341231150c1ac14e9119239816040516101ea919061020d565b60405180910390a150565b5f819050919050565b610207816101f5565b82525050565b5f6020820190506102205f8301846101fe565b92915050565b5f5ffd5b610233816101f5565b811461023d575f5ffd5b50565b5f8135905061024e8161022a565b92915050565b5f6020828403121561026957610268610226565b5b5f61027684828501610240565b91505092915050565b5f82825260208201905092915050565b7f696e76616c6964206e756d6265720000000000000000000000000000000000005f82015250565b5f6102c3600e8361027f565b91506102ce8261028f565b602082019050919050565b5f6020820190508181035f8301526102f0816102b7565b9050919050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52601160045260245ffd5b5f61032e826101f5565b9150610339836101f5565b9250828203905081811115610351576103506102f7565b5b92915050565b5f610361826101f5565b915061036c836101f5565b9250828201905080821115610384576103836102f7565b5b9291505056fea26469706673582212206904ac55654c31a4ab337443f1fb26969d3d734f5c3e16598a4f511d330f379964736f6c634300081c0033", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH1 0xE JUMPI PUSH0 PUSH0 REVERT JUMPDEST POP PUSH2 0x3C0 DUP1 PUSH2 0x1C PUSH0 CODECOPY PUSH0 RETURN INVALID PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0xF JUMPI PUSH0 PUSH0 REVERT JUMPDEST POP PUSH1 0x4 CALLDATASIZE LT PUSH2 0x4A JUMPI PUSH0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0xC55699C EQ PUSH2 0x4E JUMPI DUP1 PUSH4 0x3A9EBEFD EQ PUSH2 0x6C JUMPI DUP1 PUSH4 0x6D4CE63C EQ PUSH2 0x88 JUMPI DUP1 PUSH4 0x7CF5DAB0 EQ PUSH2 0xA6 JUMPI JUMPDEST PUSH0 PUSH0 REVERT JUMPDEST PUSH2 0x56 PUSH2 0xC2 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x63 SWAP2 SWAP1 PUSH2 0x20D JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH2 0x86 PUSH1 0x4 DUP1 CALLDATASIZE SUB DUP2 ADD SWAP1 PUSH2 0x81 SWAP2 SWAP1 PUSH2 0x254 JUMP JUMPDEST PUSH2 0xC7 JUMP JUMPDEST STOP JUMPDEST PUSH2 0x90 PUSH2 0x15A JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x9D SWAP2 SWAP1 PUSH2 0x20D JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH2 0xC0 PUSH1 0x4 DUP1 CALLDATASIZE SUB DUP2 ADD SWAP1 PUSH2 0xBB SWAP2 SWAP1 PUSH2 0x254 JUMP JUMPDEST PUSH2 0x162 JUMP JUMPDEST STOP JUMPDEST PUSH0 SLOAD DUP2 JUMP JUMPDEST PUSH0 DUP2 GT PUSH2 0x109 JUMPI PUSH1 0x40 MLOAD PUSH32 0x8C379A000000000000000000000000000000000000000000000000000000000 DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x100 SWAP1 PUSH2 0x2D9 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST DUP1 PUSH0 PUSH0 DUP3 DUP3 SLOAD PUSH2 0x119 SWAP2 SWAP1 PUSH2 0x324 JUMP JUMPDEST SWAP3 POP POP DUP2 SWAP1 SSTORE POP PUSH32 0x4C5D8C5A3755953E6D51FBC8A808109F0EFB2039C64DE8D21FEA4811A0126BF2 DUP2 PUSH1 0x40 MLOAD PUSH2 0x14F SWAP2 SWAP1 PUSH2 0x20D JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG1 POP JUMP JUMPDEST PUSH0 PUSH0 SLOAD SWAP1 POP SWAP1 JUMP JUMPDEST PUSH0 DUP2 GT PUSH2 0x1A4 JUMPI PUSH1 0x40 MLOAD PUSH32 0x8C379A000000000000000000000000000000000000000000000000000000000 DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x19B SWAP1 PUSH2 0x2D9 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST DUP1 PUSH0 PUSH0 DUP3 DUP3 SLOAD PUSH2 0x1B4 SWAP2 SWAP1 PUSH2 0x357 JUMP JUMPDEST SWAP3 POP POP DUP2 SWAP1 SSTORE POP PUSH32 0xF15468F8ED097936D939A4F3FCD54599DAD6662B341231150C1AC14E9119239 DUP2 PUSH1 0x40 MLOAD PUSH2 0x1EA SWAP2 SWAP1 PUSH2 0x20D JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG1 POP JUMP JUMPDEST PUSH0 DUP2 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x207 DUP2 PUSH2 0x1F5 JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH0 PUSH1 0x20 DUP3 ADD SWAP1 POP PUSH2 0x220 PUSH0 DUP4 ADD DUP5 PUSH2 0x1FE JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH0 PUSH0 REVERT JUMPDEST PUSH2 0x233 DUP2 PUSH2 0x1F5 JUMP JUMPDEST DUP2 EQ PUSH2 0x23D JUMPI PUSH0 PUSH0 REVERT JUMPDEST POP JUMP JUMPDEST PUSH0 DUP2 CALLDATALOAD SWAP1 POP PUSH2 0x24E DUP2 PUSH2 0x22A JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x269 JUMPI PUSH2 0x268 PUSH2 0x226 JUMP JUMPDEST JUMPDEST PUSH0 PUSH2 0x276 DUP5 DUP3 DUP6 ADD PUSH2 0x240 JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH0 DUP3 DUP3 MSTORE PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH32 0x696E76616C6964206E756D626572000000000000000000000000000000000000 PUSH0 DUP3 ADD MSTORE POP JUMP JUMPDEST PUSH0 PUSH2 0x2C3 PUSH1 0xE DUP4 PUSH2 0x27F JUMP JUMPDEST SWAP2 POP PUSH2 0x2CE DUP3 PUSH2 0x28F JUMP JUMPDEST PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH0 PUSH1 0x20 DUP3 ADD SWAP1 POP DUP2 DUP2 SUB PUSH0 DUP4 ADD MSTORE PUSH2 0x2F0 DUP2 PUSH2 0x2B7 JUMP JUMPDEST SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH0 REVERT JUMPDEST PUSH0 PUSH2 0x32E DUP3 PUSH2 0x1F5 JUMP JUMPDEST SWAP2 POP PUSH2 0x339 DUP4 PUSH2 0x1F5 JUMP JUMPDEST SWAP3 POP DUP3 DUP3 SUB SWAP1 POP DUP2 DUP2 GT ISZERO PUSH2 0x351 JUMPI PUSH2 0x350 PUSH2 0x2F7 JUMP JUMPDEST JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH0 PUSH2 0x361 DUP3 PUSH2 0x1F5 JUMP JUMPDEST SWAP2 POP PUSH2 0x36C DUP4 PUSH2 0x1F5 JUMP JUMPDEST SWAP3 POP DUP3 DUP3 ADD SWAP1 POP DUP1 DUP3 GT ISZERO PUSH2 0x384 JUMPI PUSH2 0x383 PUSH2 0x2F7 JUMP JUMPDEST JUMPDEST SWAP3 SWAP2 POP POP JUMP INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 PUSH10 0x4AC55654C31A4AB3374 NUMBER CALL 0xFB 0x26 SWAP7 SWAP14 RETURNDATASIZE PUSH20 0x4F5C3E16598A4F511D330F379964736F6C634300 ADDMOD SHR STOP CALLER ", "sourceMap": "58:443:0:-:0;;;;;;;;;;;;;;;;;;;"}, "deployedBytecode": {"functionDebugData": {"@decrement_61": {"entryPoint": 199, "id": 61, "parameterSlots": 1, "returnSlots": 0}, "@get_19": {"entryPoint": 346, "id": 19, "parameterSlots": 0, "returnSlots": 1}, "@increment_40": {"entryPoint": 354, "id": 40, "parameterSlots": 1, "returnSlots": 0}, "@x_3": {"entryPoint": 194, "id": 3, "parameterSlots": 0, "returnSlots": 0}, "abi_decode_t_uint256": {"entryPoint": 576, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_tuple_t_uint256": {"entryPoint": 596, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_t_stringliteral_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86_to_t_string_memory_ptr_fromStack": {"entryPoint": 695, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_t_uint256_to_t_uint256_fromStack": {"entryPoint": 510, "id": null, "parameterSlots": 2, "returnSlots": 0}, "abi_encode_tuple_t_stringliteral_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": 729, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed": {"entryPoint": 525, "id": null, "parameterSlots": 2, "returnSlots": 1}, "allocate_unbounded": {"entryPoint": null, "id": null, "parameterSlots": 0, "returnSlots": 1}, "array_storeLengthForEncoding_t_string_memory_ptr_fromStack": {"entryPoint": 639, "id": null, "parameterSlots": 2, "returnSlots": 1}, "checked_add_t_uint256": {"entryPoint": 855, "id": null, "parameterSlots": 2, "returnSlots": 1}, "checked_sub_t_uint256": {"entryPoint": 804, "id": null, "parameterSlots": 2, "returnSlots": 1}, "cleanup_t_uint256": {"entryPoint": 501, "id": null, "parameterSlots": 1, "returnSlots": 1}, "panic_error_0x11": {"entryPoint": 759, "id": null, "parameterSlots": 0, "returnSlots": 0}, "revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db": {"entryPoint": null, "id": null, "parameterSlots": 0, "returnSlots": 0}, "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b": {"entryPoint": 550, "id": null, "parameterSlots": 0, "returnSlots": 0}, "store_literal_in_memory_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86": {"entryPoint": 655, "id": null, "parameterSlots": 1, "returnSlots": 0}, "validator_revert_t_uint256": {"entryPoint": 554, "id": null, "parameterSlots": 1, "returnSlots": 0}}, "generatedSources": [{"ast": {"nativeSrc": "0:3099:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "0:3099:1", "statements": [{"body": {"nativeSrc": "52:32:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "52:32:1", "statements": [{"nativeSrc": "62:16:1", "nodeType": "YulAssignment", "src": "62:16:1", "value": {"name": "value", "nativeSrc": "73:5:1", "nodeType": "YulIdentifier", "src": "73:5:1"}, "variableNames": [{"name": "cleaned", "nativeSrc": "62:7:1", "nodeType": "YulIdentifier", "src": "62:7:1"}]}]}, "name": "cleanup_t_uint256", "nativeSrc": "7:77:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "34:5:1", "nodeType": "YulTypedName", "src": "34:5:1", "type": ""}], "returnVariables": [{"name": "cleaned", "nativeSrc": "44:7:1", "nodeType": "YulTypedName", "src": "44:7:1", "type": ""}], "src": "7:77:1"}, {"body": {"nativeSrc": "155:53:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "155:53:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "172:3:1", "nodeType": "YulIdentifier", "src": "172:3:1"}, {"arguments": [{"name": "value", "nativeSrc": "195:5:1", "nodeType": "YulIdentifier", "src": "195:5:1"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "177:17:1", "nodeType": "YulIdentifier", "src": "177:17:1"}, "nativeSrc": "177:24:1", "nodeType": "YulFunctionCall", "src": "177:24:1"}], "functionName": {"name": "mstore", "nativeSrc": "165:6:1", "nodeType": "YulIdentifier", "src": "165:6:1"}, "nativeSrc": "165:37:1", "nodeType": "YulFunctionCall", "src": "165:37:1"}, "nativeSrc": "165:37:1", "nodeType": "YulExpressionStatement", "src": "165:37:1"}]}, "name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nativeSrc": "90:118:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "143:5:1", "nodeType": "YulTypedName", "src": "143:5:1", "type": ""}, {"name": "pos", "nativeSrc": "150:3:1", "nodeType": "YulTypedName", "src": "150:3:1", "type": ""}], "src": "90:118:1"}, {"body": {"nativeSrc": "312:124:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "312:124:1", "statements": [{"nativeSrc": "322:26:1", "nodeType": "YulAssignment", "src": "322:26:1", "value": {"arguments": [{"name": "headStart", "nativeSrc": "334:9:1", "nodeType": "YulIdentifier", "src": "334:9:1"}, {"kind": "number", "nativeSrc": "345:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "345:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "330:3:1", "nodeType": "YulIdentifier", "src": "330:3:1"}, "nativeSrc": "330:18:1", "nodeType": "YulFunctionCall", "src": "330:18:1"}, "variableNames": [{"name": "tail", "nativeSrc": "322:4:1", "nodeType": "YulIdentifier", "src": "322:4:1"}]}, {"expression": {"arguments": [{"name": "value0", "nativeSrc": "402:6:1", "nodeType": "YulIdentifier", "src": "402:6:1"}, {"arguments": [{"name": "headStart", "nativeSrc": "415:9:1", "nodeType": "YulIdentifier", "src": "415:9:1"}, {"kind": "number", "nativeSrc": "426:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "426:1:1", "type": "", "value": "0"}], "functionName": {"name": "add", "nativeSrc": "411:3:1", "nodeType": "YulIdentifier", "src": "411:3:1"}, "nativeSrc": "411:17:1", "nodeType": "YulFunctionCall", "src": "411:17:1"}], "functionName": {"name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nativeSrc": "358:43:1", "nodeType": "YulIdentifier", "src": "358:43:1"}, "nativeSrc": "358:71:1", "nodeType": "YulFunctionCall", "src": "358:71:1"}, "nativeSrc": "358:71:1", "nodeType": "YulExpressionStatement", "src": "358:71:1"}]}, "name": "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed", "nativeSrc": "214:222:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "284:9:1", "nodeType": "YulTypedName", "src": "284:9:1", "type": ""}, {"name": "value0", "nativeSrc": "296:6:1", "nodeType": "YulTypedName", "src": "296:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "307:4:1", "nodeType": "YulTypedName", "src": "307:4:1", "type": ""}], "src": "214:222:1"}, {"body": {"nativeSrc": "482:35:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "482:35:1", "statements": [{"nativeSrc": "492:19:1", "nodeType": "YulAssignment", "src": "492:19:1", "value": {"arguments": [{"kind": "number", "nativeSrc": "508:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "508:2:1", "type": "", "value": "64"}], "functionName": {"name": "mload", "nativeSrc": "502:5:1", "nodeType": "YulIdentifier", "src": "502:5:1"}, "nativeSrc": "502:9:1", "nodeType": "YulFunctionCall", "src": "502:9:1"}, "variableNames": [{"name": "memPtr", "nativeSrc": "492:6:1", "nodeType": "YulIdentifier", "src": "492:6:1"}]}]}, "name": "allocate_unbounded", "nativeSrc": "442:75:1", "nodeType": "YulFunctionDefinition", "returnVariables": [{"name": "memPtr", "nativeSrc": "475:6:1", "nodeType": "YulTypedName", "src": "475:6:1", "type": ""}], "src": "442:75:1"}, {"body": {"nativeSrc": "612:28:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "612:28:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "629:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "629:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "632:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "632:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "622:6:1", "nodeType": "YulIdentifier", "src": "622:6:1"}, "nativeSrc": "622:12:1", "nodeType": "YulFunctionCall", "src": "622:12:1"}, "nativeSrc": "622:12:1", "nodeType": "YulExpressionStatement", "src": "622:12:1"}]}, "name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nativeSrc": "523:117:1", "nodeType": "YulFunctionDefinition", "src": "523:117:1"}, {"body": {"nativeSrc": "735:28:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "735:28:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "752:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "752:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "755:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "755:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "745:6:1", "nodeType": "YulIdentifier", "src": "745:6:1"}, "nativeSrc": "745:12:1", "nodeType": "YulFunctionCall", "src": "745:12:1"}, "nativeSrc": "745:12:1", "nodeType": "YulExpressionStatement", "src": "745:12:1"}]}, "name": "revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db", "nativeSrc": "646:117:1", "nodeType": "YulFunctionDefinition", "src": "646:117:1"}, {"body": {"nativeSrc": "812:79:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "812:79:1", "statements": [{"body": {"nativeSrc": "869:16:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "869:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "878:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "878:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "881:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "881:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "871:6:1", "nodeType": "YulIdentifier", "src": "871:6:1"}, "nativeSrc": "871:12:1", "nodeType": "YulFunctionCall", "src": "871:12:1"}, "nativeSrc": "871:12:1", "nodeType": "YulExpressionStatement", "src": "871:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "835:5:1", "nodeType": "YulIdentifier", "src": "835:5:1"}, {"arguments": [{"name": "value", "nativeSrc": "860:5:1", "nodeType": "YulIdentifier", "src": "860:5:1"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "842:17:1", "nodeType": "YulIdentifier", "src": "842:17:1"}, "nativeSrc": "842:24:1", "nodeType": "YulFunctionCall", "src": "842:24:1"}], "functionName": {"name": "eq", "nativeSrc": "832:2:1", "nodeType": "YulIdentifier", "src": "832:2:1"}, "nativeSrc": "832:35:1", "nodeType": "YulFunctionCall", "src": "832:35:1"}], "functionName": {"name": "iszero", "nativeSrc": "825:6:1", "nodeType": "YulIdentifier", "src": "825:6:1"}, "nativeSrc": "825:43:1", "nodeType": "YulFunctionCall", "src": "825:43:1"}, "nativeSrc": "822:63:1", "nodeType": "YulIf", "src": "822:63:1"}]}, "name": "validator_revert_t_uint256", "nativeSrc": "769:122:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "805:5:1", "nodeType": "YulTypedName", "src": "805:5:1", "type": ""}], "src": "769:122:1"}, {"body": {"nativeSrc": "949:87:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "949:87:1", "statements": [{"nativeSrc": "959:29:1", "nodeType": "YulAssignment", "src": "959:29:1", "value": {"arguments": [{"name": "offset", "nativeSrc": "981:6:1", "nodeType": "YulIdentifier", "src": "981:6:1"}], "functionName": {"name": "calldataload", "nativeSrc": "968:12:1", "nodeType": "YulIdentifier", "src": "968:12:1"}, "nativeSrc": "968:20:1", "nodeType": "YulFunctionCall", "src": "968:20:1"}, "variableNames": [{"name": "value", "nativeSrc": "959:5:1", "nodeType": "YulIdentifier", "src": "959:5:1"}]}, {"expression": {"arguments": [{"name": "value", "nativeSrc": "1024:5:1", "nodeType": "YulIdentifier", "src": "1024:5:1"}], "functionName": {"name": "validator_revert_t_uint256", "nativeSrc": "997:26:1", "nodeType": "YulIdentifier", "src": "997:26:1"}, "nativeSrc": "997:33:1", "nodeType": "YulFunctionCall", "src": "997:33:1"}, "nativeSrc": "997:33:1", "nodeType": "YulExpressionStatement", "src": "997:33:1"}]}, "name": "abi_decode_t_uint256", "nativeSrc": "897:139:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nativeSrc": "927:6:1", "nodeType": "YulTypedName", "src": "927:6:1", "type": ""}, {"name": "end", "nativeSrc": "935:3:1", "nodeType": "YulTypedName", "src": "935:3:1", "type": ""}], "returnVariables": [{"name": "value", "nativeSrc": "943:5:1", "nodeType": "YulTypedName", "src": "943:5:1", "type": ""}], "src": "897:139:1"}, {"body": {"nativeSrc": "1108:263:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1108:263:1", "statements": [{"body": {"nativeSrc": "1154:83:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1154:83:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nativeSrc": "1156:77:1", "nodeType": "YulIdentifier", "src": "1156:77:1"}, "nativeSrc": "1156:79:1", "nodeType": "YulFunctionCall", "src": "1156:79:1"}, "nativeSrc": "1156:79:1", "nodeType": "YulExpressionStatement", "src": "1156:79:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nativeSrc": "1129:7:1", "nodeType": "YulIdentifier", "src": "1129:7:1"}, {"name": "headStart", "nativeSrc": "1138:9:1", "nodeType": "YulIdentifier", "src": "1138:9:1"}], "functionName": {"name": "sub", "nativeSrc": "1125:3:1", "nodeType": "YulIdentifier", "src": "1125:3:1"}, "nativeSrc": "1125:23:1", "nodeType": "YulFunctionCall", "src": "1125:23:1"}, {"kind": "number", "nativeSrc": "1150:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1150:2:1", "type": "", "value": "32"}], "functionName": {"name": "slt", "nativeSrc": "1121:3:1", "nodeType": "YulIdentifier", "src": "1121:3:1"}, "nativeSrc": "1121:32:1", "nodeType": "YulFunctionCall", "src": "1121:32:1"}, "nativeSrc": "1118:119:1", "nodeType": "YulIf", "src": "1118:119:1"}, {"nativeSrc": "1247:117:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1247:117:1", "statements": [{"nativeSrc": "1262:15:1", "nodeType": "YulVariableDeclaration", "src": "1262:15:1", "value": {"kind": "number", "nativeSrc": "1276:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1276:1:1", "type": "", "value": "0"}, "variables": [{"name": "offset", "nativeSrc": "1266:6:1", "nodeType": "YulTypedName", "src": "1266:6:1", "type": ""}]}, {"nativeSrc": "1291:63:1", "nodeType": "YulAssignment", "src": "1291:63:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "1326:9:1", "nodeType": "YulIdentifier", "src": "1326:9:1"}, {"name": "offset", "nativeSrc": "1337:6:1", "nodeType": "YulIdentifier", "src": "1337:6:1"}], "functionName": {"name": "add", "nativeSrc": "1322:3:1", "nodeType": "YulIdentifier", "src": "1322:3:1"}, "nativeSrc": "1322:22:1", "nodeType": "YulFunctionCall", "src": "1322:22:1"}, {"name": "dataEnd", "nativeSrc": "1346:7:1", "nodeType": "YulIdentifier", "src": "1346:7:1"}], "functionName": {"name": "abi_decode_t_uint256", "nativeSrc": "1301:20:1", "nodeType": "YulIdentifier", "src": "1301:20:1"}, "nativeSrc": "1301:53:1", "nodeType": "YulFunctionCall", "src": "1301:53:1"}, "variableNames": [{"name": "value0", "nativeSrc": "1291:6:1", "nodeType": "YulIdentifier", "src": "1291:6:1"}]}]}]}, "name": "abi_decode_tuple_t_uint256", "nativeSrc": "1042:329:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "1078:9:1", "nodeType": "YulTypedName", "src": "1078:9:1", "type": ""}, {"name": "dataEnd", "nativeSrc": "1089:7:1", "nodeType": "YulTypedName", "src": "1089:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nativeSrc": "1101:6:1", "nodeType": "YulTypedName", "src": "1101:6:1", "type": ""}], "src": "1042:329:1"}, {"body": {"nativeSrc": "1473:73:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1473:73:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "1490:3:1", "nodeType": "YulIdentifier", "src": "1490:3:1"}, {"name": "length", "nativeSrc": "1495:6:1", "nodeType": "YulIdentifier", "src": "1495:6:1"}], "functionName": {"name": "mstore", "nativeSrc": "1483:6:1", "nodeType": "YulIdentifier", "src": "1483:6:1"}, "nativeSrc": "1483:19:1", "nodeType": "YulFunctionCall", "src": "1483:19:1"}, "nativeSrc": "1483:19:1", "nodeType": "YulExpressionStatement", "src": "1483:19:1"}, {"nativeSrc": "1511:29:1", "nodeType": "YulAssignment", "src": "1511:29:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "1530:3:1", "nodeType": "YulIdentifier", "src": "1530:3:1"}, {"kind": "number", "nativeSrc": "1535:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1535:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "1526:3:1", "nodeType": "YulIdentifier", "src": "1526:3:1"}, "nativeSrc": "1526:14:1", "nodeType": "YulFunctionCall", "src": "1526:14:1"}, "variableNames": [{"name": "updated_pos", "nativeSrc": "1511:11:1", "nodeType": "YulIdentifier", "src": "1511:11:1"}]}]}, "name": "array_storeLengthForEncoding_t_string_memory_ptr_fromStack", "nativeSrc": "1377:169:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nativeSrc": "1445:3:1", "nodeType": "YulTypedName", "src": "1445:3:1", "type": ""}, {"name": "length", "nativeSrc": "1450:6:1", "nodeType": "YulTypedName", "src": "1450:6:1", "type": ""}], "returnVariables": [{"name": "updated_pos", "nativeSrc": "1461:11:1", "nodeType": "YulTypedName", "src": "1461:11:1", "type": ""}], "src": "1377:169:1"}, {"body": {"nativeSrc": "1658:58:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1658:58:1", "statements": [{"expression": {"arguments": [{"arguments": [{"name": "memPtr", "nativeSrc": "1680:6:1", "nodeType": "YulIdentifier", "src": "1680:6:1"}, {"kind": "number", "nativeSrc": "1688:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1688:1:1", "type": "", "value": "0"}], "functionName": {"name": "add", "nativeSrc": "1676:3:1", "nodeType": "YulIdentifier", "src": "1676:3:1"}, "nativeSrc": "1676:14:1", "nodeType": "YulFunctionCall", "src": "1676:14:1"}, {"hexValue": "696e76616c6964206e756d626572", "kind": "string", "nativeSrc": "1692:16:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1692:16:1", "type": "", "value": "invalid number"}], "functionName": {"name": "mstore", "nativeSrc": "1669:6:1", "nodeType": "YulIdentifier", "src": "1669:6:1"}, "nativeSrc": "1669:40:1", "nodeType": "YulFunctionCall", "src": "1669:40:1"}, "nativeSrc": "1669:40:1", "nodeType": "YulExpressionStatement", "src": "1669:40:1"}]}, "name": "store_literal_in_memory_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86", "nativeSrc": "1552:164:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "memPtr", "nativeSrc": "1650:6:1", "nodeType": "YulTypedName", "src": "1650:6:1", "type": ""}], "src": "1552:164:1"}, {"body": {"nativeSrc": "1868:220:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1868:220:1", "statements": [{"nativeSrc": "1878:74:1", "nodeType": "YulAssignment", "src": "1878:74:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "1944:3:1", "nodeType": "YulIdentifier", "src": "1944:3:1"}, {"kind": "number", "nativeSrc": "1949:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1949:2:1", "type": "", "value": "14"}], "functionName": {"name": "array_storeLengthForEncoding_t_string_memory_ptr_fromStack", "nativeSrc": "1885:58:1", "nodeType": "YulIdentifier", "src": "1885:58:1"}, "nativeSrc": "1885:67:1", "nodeType": "YulFunctionCall", "src": "1885:67:1"}, "variableNames": [{"name": "pos", "nativeSrc": "1878:3:1", "nodeType": "YulIdentifier", "src": "1878:3:1"}]}, {"expression": {"arguments": [{"name": "pos", "nativeSrc": "2050:3:1", "nodeType": "YulIdentifier", "src": "2050:3:1"}], "functionName": {"name": "store_literal_in_memory_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86", "nativeSrc": "1961:88:1", "nodeType": "YulIdentifier", "src": "1961:88:1"}, "nativeSrc": "1961:93:1", "nodeType": "YulFunctionCall", "src": "1961:93:1"}, "nativeSrc": "1961:93:1", "nodeType": "YulExpressionStatement", "src": "1961:93:1"}, {"nativeSrc": "2063:19:1", "nodeType": "YulAssignment", "src": "2063:19:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "2074:3:1", "nodeType": "YulIdentifier", "src": "2074:3:1"}, {"kind": "number", "nativeSrc": "2079:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2079:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "2070:3:1", "nodeType": "YulIdentifier", "src": "2070:3:1"}, "nativeSrc": "2070:12:1", "nodeType": "YulFunctionCall", "src": "2070:12:1"}, "variableNames": [{"name": "end", "nativeSrc": "2063:3:1", "nodeType": "YulIdentifier", "src": "2063:3:1"}]}]}, "name": "abi_encode_t_stringliteral_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86_to_t_string_memory_ptr_fromStack", "nativeSrc": "1722:366:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nativeSrc": "1856:3:1", "nodeType": "YulTypedName", "src": "1856:3:1", "type": ""}], "returnVariables": [{"name": "end", "nativeSrc": "1864:3:1", "nodeType": "YulTypedName", "src": "1864:3:1", "type": ""}], "src": "1722:366:1"}, {"body": {"nativeSrc": "2265:248:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2265:248:1", "statements": [{"nativeSrc": "2275:26:1", "nodeType": "YulAssignment", "src": "2275:26:1", "value": {"arguments": [{"name": "headStart", "nativeSrc": "2287:9:1", "nodeType": "YulIdentifier", "src": "2287:9:1"}, {"kind": "number", "nativeSrc": "2298:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2298:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "2283:3:1", "nodeType": "YulIdentifier", "src": "2283:3:1"}, "nativeSrc": "2283:18:1", "nodeType": "YulFunctionCall", "src": "2283:18:1"}, "variableNames": [{"name": "tail", "nativeSrc": "2275:4:1", "nodeType": "YulIdentifier", "src": "2275:4:1"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "2322:9:1", "nodeType": "YulIdentifier", "src": "2322:9:1"}, {"kind": "number", "nativeSrc": "2333:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2333:1:1", "type": "", "value": "0"}], "functionName": {"name": "add", "nativeSrc": "2318:3:1", "nodeType": "YulIdentifier", "src": "2318:3:1"}, "nativeSrc": "2318:17:1", "nodeType": "YulFunctionCall", "src": "2318:17:1"}, {"arguments": [{"name": "tail", "nativeSrc": "2341:4:1", "nodeType": "YulIdentifier", "src": "2341:4:1"}, {"name": "headStart", "nativeSrc": "2347:9:1", "nodeType": "YulIdentifier", "src": "2347:9:1"}], "functionName": {"name": "sub", "nativeSrc": "2337:3:1", "nodeType": "YulIdentifier", "src": "2337:3:1"}, "nativeSrc": "2337:20:1", "nodeType": "YulFunctionCall", "src": "2337:20:1"}], "functionName": {"name": "mstore", "nativeSrc": "2311:6:1", "nodeType": "YulIdentifier", "src": "2311:6:1"}, "nativeSrc": "2311:47:1", "nodeType": "YulFunctionCall", "src": "2311:47:1"}, "nativeSrc": "2311:47:1", "nodeType": "YulExpressionStatement", "src": "2311:47:1"}, {"nativeSrc": "2367:139:1", "nodeType": "YulAssignment", "src": "2367:139:1", "value": {"arguments": [{"name": "tail", "nativeSrc": "2501:4:1", "nodeType": "YulIdentifier", "src": "2501:4:1"}], "functionName": {"name": "abi_encode_t_stringliteral_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86_to_t_string_memory_ptr_fromStack", "nativeSrc": "2375:124:1", "nodeType": "YulIdentifier", "src": "2375:124:1"}, "nativeSrc": "2375:131:1", "nodeType": "YulFunctionCall", "src": "2375:131:1"}, "variableNames": [{"name": "tail", "nativeSrc": "2367:4:1", "nodeType": "YulIdentifier", "src": "2367:4:1"}]}]}, "name": "abi_encode_tuple_t_stringliteral_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86__to_t_string_memory_ptr__fromStack_reversed", "nativeSrc": "2094:419:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "2245:9:1", "nodeType": "YulTypedName", "src": "2245:9:1", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "2260:4:1", "nodeType": "YulTypedName", "src": "2260:4:1", "type": ""}], "src": "2094:419:1"}, {"body": {"nativeSrc": "2547:152:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2547:152:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "2564:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2564:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "2567:77:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2567:77:1", "type": "", "value": "35408467139433450592217433187231851964531694900788300625387963629091585785856"}], "functionName": {"name": "mstore", "nativeSrc": "2557:6:1", "nodeType": "YulIdentifier", "src": "2557:6:1"}, "nativeSrc": "2557:88:1", "nodeType": "YulFunctionCall", "src": "2557:88:1"}, "nativeSrc": "2557:88:1", "nodeType": "YulExpressionStatement", "src": "2557:88:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "2661:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2661:1:1", "type": "", "value": "4"}, {"kind": "number", "nativeSrc": "2664:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2664:4:1", "type": "", "value": "0x11"}], "functionName": {"name": "mstore", "nativeSrc": "2654:6:1", "nodeType": "YulIdentifier", "src": "2654:6:1"}, "nativeSrc": "2654:15:1", "nodeType": "YulFunctionCall", "src": "2654:15:1"}, "nativeSrc": "2654:15:1", "nodeType": "YulExpressionStatement", "src": "2654:15:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "2685:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2685:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "2688:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2688:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nativeSrc": "2678:6:1", "nodeType": "YulIdentifier", "src": "2678:6:1"}, "nativeSrc": "2678:15:1", "nodeType": "YulFunctionCall", "src": "2678:15:1"}, "nativeSrc": "2678:15:1", "nodeType": "YulExpressionStatement", "src": "2678:15:1"}]}, "name": "panic_error_0x11", "nativeSrc": "2519:180:1", "nodeType": "YulFunctionDefinition", "src": "2519:180:1"}, {"body": {"nativeSrc": "2750:149:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2750:149:1", "statements": [{"nativeSrc": "2760:25:1", "nodeType": "YulAssignment", "src": "2760:25:1", "value": {"arguments": [{"name": "x", "nativeSrc": "2783:1:1", "nodeType": "YulIdentifier", "src": "2783:1:1"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "2765:17:1", "nodeType": "YulIdentifier", "src": "2765:17:1"}, "nativeSrc": "2765:20:1", "nodeType": "YulFunctionCall", "src": "2765:20:1"}, "variableNames": [{"name": "x", "nativeSrc": "2760:1:1", "nodeType": "YulIdentifier", "src": "2760:1:1"}]}, {"nativeSrc": "2794:25:1", "nodeType": "YulAssignment", "src": "2794:25:1", "value": {"arguments": [{"name": "y", "nativeSrc": "2817:1:1", "nodeType": "YulIdentifier", "src": "2817:1:1"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "2799:17:1", "nodeType": "YulIdentifier", "src": "2799:17:1"}, "nativeSrc": "2799:20:1", "nodeType": "YulFunctionCall", "src": "2799:20:1"}, "variableNames": [{"name": "y", "nativeSrc": "2794:1:1", "nodeType": "YulIdentifier", "src": "2794:1:1"}]}, {"nativeSrc": "2828:17:1", "nodeType": "YulAssignment", "src": "2828:17:1", "value": {"arguments": [{"name": "x", "nativeSrc": "2840:1:1", "nodeType": "YulIdentifier", "src": "2840:1:1"}, {"name": "y", "nativeSrc": "2843:1:1", "nodeType": "YulIdentifier", "src": "2843:1:1"}], "functionName": {"name": "sub", "nativeSrc": "2836:3:1", "nodeType": "YulIdentifier", "src": "2836:3:1"}, "nativeSrc": "2836:9:1", "nodeType": "YulFunctionCall", "src": "2836:9:1"}, "variableNames": [{"name": "diff", "nativeSrc": "2828:4:1", "nodeType": "YulIdentifier", "src": "2828:4:1"}]}, {"body": {"nativeSrc": "2870:22:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2870:22:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x11", "nativeSrc": "2872:16:1", "nodeType": "YulIdentifier", "src": "2872:16:1"}, "nativeSrc": "2872:18:1", "nodeType": "YulFunctionCall", "src": "2872:18:1"}, "nativeSrc": "2872:18:1", "nodeType": "YulExpressionStatement", "src": "2872:18:1"}]}, "condition": {"arguments": [{"name": "diff", "nativeSrc": "2861:4:1", "nodeType": "YulIdentifier", "src": "2861:4:1"}, {"name": "x", "nativeSrc": "2867:1:1", "nodeType": "YulIdentifier", "src": "2867:1:1"}], "functionName": {"name": "gt", "nativeSrc": "2858:2:1", "nodeType": "YulIdentifier", "src": "2858:2:1"}, "nativeSrc": "2858:11:1", "nodeType": "YulFunctionCall", "src": "2858:11:1"}, "nativeSrc": "2855:37:1", "nodeType": "YulIf", "src": "2855:37:1"}]}, "name": "checked_sub_t_uint256", "nativeSrc": "2705:194:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "x", "nativeSrc": "2736:1:1", "nodeType": "YulTypedName", "src": "2736:1:1", "type": ""}, {"name": "y", "nativeSrc": "2739:1:1", "nodeType": "YulTypedName", "src": "2739:1:1", "type": ""}], "returnVariables": [{"name": "diff", "nativeSrc": "2745:4:1", "nodeType": "YulTypedName", "src": "2745:4:1", "type": ""}], "src": "2705:194:1"}, {"body": {"nativeSrc": "2949:147:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2949:147:1", "statements": [{"nativeSrc": "2959:25:1", "nodeType": "YulAssignment", "src": "2959:25:1", "value": {"arguments": [{"name": "x", "nativeSrc": "2982:1:1", "nodeType": "YulIdentifier", "src": "2982:1:1"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "2964:17:1", "nodeType": "YulIdentifier", "src": "2964:17:1"}, "nativeSrc": "2964:20:1", "nodeType": "YulFunctionCall", "src": "2964:20:1"}, "variableNames": [{"name": "x", "nativeSrc": "2959:1:1", "nodeType": "YulIdentifier", "src": "2959:1:1"}]}, {"nativeSrc": "2993:25:1", "nodeType": "YulAssignment", "src": "2993:25:1", "value": {"arguments": [{"name": "y", "nativeSrc": "3016:1:1", "nodeType": "YulIdentifier", "src": "3016:1:1"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "2998:17:1", "nodeType": "YulIdentifier", "src": "2998:17:1"}, "nativeSrc": "2998:20:1", "nodeType": "YulFunctionCall", "src": "2998:20:1"}, "variableNames": [{"name": "y", "nativeSrc": "2993:1:1", "nodeType": "YulIdentifier", "src": "2993:1:1"}]}, {"nativeSrc": "3027:16:1", "nodeType": "YulAssignment", "src": "3027:16:1", "value": {"arguments": [{"name": "x", "nativeSrc": "3038:1:1", "nodeType": "YulIdentifier", "src": "3038:1:1"}, {"name": "y", "nativeSrc": "3041:1:1", "nodeType": "YulIdentifier", "src": "3041:1:1"}], "functionName": {"name": "add", "nativeSrc": "3034:3:1", "nodeType": "YulIdentifier", "src": "3034:3:1"}, "nativeSrc": "3034:9:1", "nodeType": "YulFunctionCall", "src": "3034:9:1"}, "variableNames": [{"name": "sum", "nativeSrc": "3027:3:1", "nodeType": "YulIdentifier", "src": "3027:3:1"}]}, {"body": {"nativeSrc": "3067:22:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3067:22:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x11", "nativeSrc": "3069:16:1", "nodeType": "YulIdentifier", "src": "3069:16:1"}, "nativeSrc": "3069:18:1", "nodeType": "YulFunctionCall", "src": "3069:18:1"}, "nativeSrc": "3069:18:1", "nodeType": "YulExpressionStatement", "src": "3069:18:1"}]}, "condition": {"arguments": [{"name": "x", "nativeSrc": "3059:1:1", "nodeType": "YulIdentifier", "src": "3059:1:1"}, {"name": "sum", "nativeSrc": "3062:3:1", "nodeType": "YulIdentifier", "src": "3062:3:1"}], "functionName": {"name": "gt", "nativeSrc": "3056:2:1", "nodeType": "YulIdentifier", "src": "3056:2:1"}, "nativeSrc": "3056:10:1", "nodeType": "YulFunctionCall", "src": "3056:10:1"}, "nativeSrc": "3053:36:1", "nodeType": "YulIf", "src": "3053:36:1"}]}, "name": "checked_add_t_uint256", "nativeSrc": "2905:191:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "x", "nativeSrc": "2936:1:1", "nodeType": "YulTypedName", "src": "2936:1:1", "type": ""}, {"name": "y", "nativeSrc": "2939:1:1", "nodeType": "YulTypedName", "src": "2939:1:1", "type": ""}], "returnVariables": [{"name": "sum", "nativeSrc": "2945:3:1", "nodeType": "YulTypedName", "src": "2945:3:1", "type": ""}], "src": "2905:191:1"}]}, "contents": "{\n\n    function cleanup_t_uint256(value) -> cleaned {\n        cleaned := value\n    }\n\n    function abi_encode_t_uint256_to_t_uint256_fromStack(value, pos) {\n        mstore(pos, cleanup_t_uint256(value))\n    }\n\n    function abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed(headStart , value0) -> tail {\n        tail := add(headStart, 32)\n\n        abi_encode_t_uint256_to_t_uint256_fromStack(value0,  add(headStart, 0))\n\n    }\n\n    function allocate_unbounded() -> memPtr {\n        memPtr := mload(64)\n    }\n\n    function revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() {\n        revert(0, 0)\n    }\n\n    function revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db() {\n        revert(0, 0)\n    }\n\n    function validator_revert_t_uint256(value) {\n        if iszero(eq(value, cleanup_t_uint256(value))) { revert(0, 0) }\n    }\n\n    function abi_decode_t_uint256(offset, end) -> value {\n        value := calldataload(offset)\n        validator_revert_t_uint256(value)\n    }\n\n    function abi_decode_tuple_t_uint256(headStart, dataEnd) -> value0 {\n        if slt(sub(dataEnd, headStart), 32) { revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() }\n\n        {\n\n            let offset := 0\n\n            value0 := abi_decode_t_uint256(add(headStart, offset), dataEnd)\n        }\n\n    }\n\n    function array_storeLengthForEncoding_t_string_memory_ptr_fromStack(pos, length) -> updated_pos {\n        mstore(pos, length)\n        updated_pos := add(pos, 0x20)\n    }\n\n    function store_literal_in_memory_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86(memPtr) {\n\n        mstore(add(memPtr, 0), \"invalid number\")\n\n    }\n\n    function abi_encode_t_stringliteral_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86_to_t_string_memory_ptr_fromStack(pos) -> end {\n        pos := array_storeLengthForEncoding_t_string_memory_ptr_fromStack(pos, 14)\n        store_literal_in_memory_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86(pos)\n        end := add(pos, 32)\n    }\n\n    function abi_encode_tuple_t_stringliteral_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86__to_t_string_memory_ptr__fromStack_reversed(headStart ) -> tail {\n        tail := add(headStart, 32)\n\n        mstore(add(headStart, 0), sub(tail, headStart))\n        tail := abi_encode_t_stringliteral_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86_to_t_string_memory_ptr_fromStack( tail)\n\n    }\n\n    function panic_error_0x11() {\n        mstore(0, 35408467139433450592217433187231851964531694900788300625387963629091585785856)\n        mstore(4, 0x11)\n        revert(0, 0x24)\n    }\n\n    function checked_sub_t_uint256(x, y) -> diff {\n        x := cleanup_t_uint256(x)\n        y := cleanup_t_uint256(y)\n        diff := sub(x, y)\n\n        if gt(diff, x) { panic_error_0x11() }\n\n    }\n\n    function checked_add_t_uint256(x, y) -> sum {\n        x := cleanup_t_uint256(x)\n        y := cleanup_t_uint256(y)\n        sum := add(x, y)\n\n        if gt(x, sum) { panic_error_0x11() }\n\n    }\n\n}\n", "id": 1, "language": "<PERSON>l", "name": "#utility.yul"}], "immutableReferences": {}, "linkReferences": {}, "object": "608060405234801561000f575f5ffd5b506004361061004a575f3560e01c80630c55699c1461004e5780633a9ebefd1461006c5780636d4ce63c146100885780637cf5dab0146100a6575b5f5ffd5b6100566100c2565b604051610063919061020d565b60405180910390f35b61008660048036038101906100819190610254565b6100c7565b005b61009061015a565b60405161009d919061020d565b60405180910390f35b6100c060048036038101906100bb9190610254565b610162565b005b5f5481565b5f8111610109576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610100906102d9565b60405180910390fd5b805f5f8282546101199190610324565b925050819055507f4c5d8c5a3755953e6d51fbc8a808109f0efb2039c64de8d21fea4811a0126bf28160405161014f919061020d565b60405180910390a150565b5f5f54905090565b5f81116101a4576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161019b906102d9565b60405180910390fd5b805f5f8282546101b49190610357565b925050819055507f0f15468f8ed097936d939a4f3fcd54599dad6662b341231150c1ac14e9119239816040516101ea919061020d565b60405180910390a150565b5f819050919050565b610207816101f5565b82525050565b5f6020820190506102205f8301846101fe565b92915050565b5f5ffd5b610233816101f5565b811461023d575f5ffd5b50565b5f8135905061024e8161022a565b92915050565b5f6020828403121561026957610268610226565b5b5f61027684828501610240565b91505092915050565b5f82825260208201905092915050565b7f696e76616c6964206e756d6265720000000000000000000000000000000000005f82015250565b5f6102c3600e8361027f565b91506102ce8261028f565b602082019050919050565b5f6020820190508181035f8301526102f0816102b7565b9050919050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52601160045260245ffd5b5f61032e826101f5565b9150610339836101f5565b9250828203905081811115610351576103506102f7565b5b92915050565b5f610361826101f5565b915061036c836101f5565b9250828201905080821115610384576103836102f7565b5b9291505056fea26469706673582212206904ac55654c31a4ab337443f1fb26969d3d734f5c3e16598a4f511d330f379964736f6c634300081c0033", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0xF JUMPI PUSH0 PUSH0 REVERT JUMPDEST POP PUSH1 0x4 CALLDATASIZE LT PUSH2 0x4A JUMPI PUSH0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0xC55699C EQ PUSH2 0x4E JUMPI DUP1 PUSH4 0x3A9EBEFD EQ PUSH2 0x6C JUMPI DUP1 PUSH4 0x6D4CE63C EQ PUSH2 0x88 JUMPI DUP1 PUSH4 0x7CF5DAB0 EQ PUSH2 0xA6 JUMPI JUMPDEST PUSH0 PUSH0 REVERT JUMPDEST PUSH2 0x56 PUSH2 0xC2 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x63 SWAP2 SWAP1 PUSH2 0x20D JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH2 0x86 PUSH1 0x4 DUP1 CALLDATASIZE SUB DUP2 ADD SWAP1 PUSH2 0x81 SWAP2 SWAP1 PUSH2 0x254 JUMP JUMPDEST PUSH2 0xC7 JUMP JUMPDEST STOP JUMPDEST PUSH2 0x90 PUSH2 0x15A JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x9D SWAP2 SWAP1 PUSH2 0x20D JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH2 0xC0 PUSH1 0x4 DUP1 CALLDATASIZE SUB DUP2 ADD SWAP1 PUSH2 0xBB SWAP2 SWAP1 PUSH2 0x254 JUMP JUMPDEST PUSH2 0x162 JUMP JUMPDEST STOP JUMPDEST PUSH0 SLOAD DUP2 JUMP JUMPDEST PUSH0 DUP2 GT PUSH2 0x109 JUMPI PUSH1 0x40 MLOAD PUSH32 0x8C379A000000000000000000000000000000000000000000000000000000000 DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x100 SWAP1 PUSH2 0x2D9 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST DUP1 PUSH0 PUSH0 DUP3 DUP3 SLOAD PUSH2 0x119 SWAP2 SWAP1 PUSH2 0x324 JUMP JUMPDEST SWAP3 POP POP DUP2 SWAP1 SSTORE POP PUSH32 0x4C5D8C5A3755953E6D51FBC8A808109F0EFB2039C64DE8D21FEA4811A0126BF2 DUP2 PUSH1 0x40 MLOAD PUSH2 0x14F SWAP2 SWAP1 PUSH2 0x20D JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG1 POP JUMP JUMPDEST PUSH0 PUSH0 SLOAD SWAP1 POP SWAP1 JUMP JUMPDEST PUSH0 DUP2 GT PUSH2 0x1A4 JUMPI PUSH1 0x40 MLOAD PUSH32 0x8C379A000000000000000000000000000000000000000000000000000000000 DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x19B SWAP1 PUSH2 0x2D9 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST DUP1 PUSH0 PUSH0 DUP3 DUP3 SLOAD PUSH2 0x1B4 SWAP2 SWAP1 PUSH2 0x357 JUMP JUMPDEST SWAP3 POP POP DUP2 SWAP1 SSTORE POP PUSH32 0xF15468F8ED097936D939A4F3FCD54599DAD6662B341231150C1AC14E9119239 DUP2 PUSH1 0x40 MLOAD PUSH2 0x1EA SWAP2 SWAP1 PUSH2 0x20D JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG1 POP JUMP JUMPDEST PUSH0 DUP2 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x207 DUP2 PUSH2 0x1F5 JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH0 PUSH1 0x20 DUP3 ADD SWAP1 POP PUSH2 0x220 PUSH0 DUP4 ADD DUP5 PUSH2 0x1FE JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH0 PUSH0 REVERT JUMPDEST PUSH2 0x233 DUP2 PUSH2 0x1F5 JUMP JUMPDEST DUP2 EQ PUSH2 0x23D JUMPI PUSH0 PUSH0 REVERT JUMPDEST POP JUMP JUMPDEST PUSH0 DUP2 CALLDATALOAD SWAP1 POP PUSH2 0x24E DUP2 PUSH2 0x22A JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x269 JUMPI PUSH2 0x268 PUSH2 0x226 JUMP JUMPDEST JUMPDEST PUSH0 PUSH2 0x276 DUP5 DUP3 DUP6 ADD PUSH2 0x240 JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH0 DUP3 DUP3 MSTORE PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH32 0x696E76616C6964206E756D626572000000000000000000000000000000000000 PUSH0 DUP3 ADD MSTORE POP JUMP JUMPDEST PUSH0 PUSH2 0x2C3 PUSH1 0xE DUP4 PUSH2 0x27F JUMP JUMPDEST SWAP2 POP PUSH2 0x2CE DUP3 PUSH2 0x28F JUMP JUMPDEST PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH0 PUSH1 0x20 DUP3 ADD SWAP1 POP DUP2 DUP2 SUB PUSH0 DUP4 ADD MSTORE PUSH2 0x2F0 DUP2 PUSH2 0x2B7 JUMP JUMPDEST SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH0 REVERT JUMPDEST PUSH0 PUSH2 0x32E DUP3 PUSH2 0x1F5 JUMP JUMPDEST SWAP2 POP PUSH2 0x339 DUP4 PUSH2 0x1F5 JUMP JUMPDEST SWAP3 POP DUP3 DUP3 SUB SWAP1 POP DUP2 DUP2 GT ISZERO PUSH2 0x351 JUMPI PUSH2 0x350 PUSH2 0x2F7 JUMP JUMPDEST JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH0 PUSH2 0x361 DUP3 PUSH2 0x1F5 JUMP JUMPDEST SWAP2 POP PUSH2 0x36C DUP4 PUSH2 0x1F5 JUMP JUMPDEST SWAP3 POP DUP3 DUP3 ADD SWAP1 POP DUP1 DUP3 GT ISZERO PUSH2 0x384 JUMPI PUSH2 0x383 PUSH2 0x2F7 JUMP JUMPDEST JUMPDEST SWAP3 SWAP2 POP POP JUMP INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 PUSH10 0x4AC55654C31A4AB3374 NUMBER CALL 0xFB 0x26 SWAP7 SWAP14 RETURNDATASIZE PUSH20 0x4F5C3E16598A4F511D330F379964736F6C634300 ADDMOD SHR STOP CALLER ", "sourceMap": "58:443:0:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;81:13;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;371:128;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;166:67;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;239:127;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;81:13;;;;:::o;371:128::-;427:1;423;:5;415:32;;;;;;;;;;;;:::i;:::-;;;;;;;;;462:1;457;;:6;;;;;;;:::i;:::-;;;;;;;;478:14;490:1;478:14;;;;;;:::i;:::-;;;;;;;;371:128;:::o;166:67::-;202:4;225:1;;218:8;;166:67;:::o;239:127::-;295:1;291;:5;283:32;;;;;;;;;;;;:::i;:::-;;;;;;;;;330:1;325;;:6;;;;;;;:::i;:::-;;;;;;;;346:13;357:1;346:13;;;;;;:::i;:::-;;;;;;;;239:127;:::o;7:77:1:-;44:7;73:5;62:16;;7:77;;;:::o;90:118::-;177:24;195:5;177:24;:::i;:::-;172:3;165:37;90:118;;:::o;214:222::-;307:4;345:2;334:9;330:18;322:26;;358:71;426:1;415:9;411:17;402:6;358:71;:::i;:::-;214:222;;;;:::o;523:117::-;632:1;629;622:12;769:122;842:24;860:5;842:24;:::i;:::-;835:5;832:35;822:63;;881:1;878;871:12;822:63;769:122;:::o;897:139::-;943:5;981:6;968:20;959:29;;997:33;1024:5;997:33;:::i;:::-;897:139;;;;:::o;1042:329::-;1101:6;1150:2;1138:9;1129:7;1125:23;1121:32;1118:119;;;1156:79;;:::i;:::-;1118:119;1276:1;1301:53;1346:7;1337:6;1326:9;1322:22;1301:53;:::i;:::-;1291:63;;1247:117;1042:329;;;;:::o;1377:169::-;1461:11;1495:6;1490:3;1483:19;1535:4;1530:3;1526:14;1511:29;;1377:169;;;;:::o;1552:164::-;1692:16;1688:1;1680:6;1676:14;1669:40;1552:164;:::o;1722:366::-;1864:3;1885:67;1949:2;1944:3;1885:67;:::i;:::-;1878:74;;1961:93;2050:3;1961:93;:::i;:::-;2079:2;2074:3;2070:12;2063:19;;1722:366;;;:::o;2094:419::-;2260:4;2298:2;2287:9;2283:18;2275:26;;2347:9;2341:4;2337:20;2333:1;2322:9;2318:17;2311:47;2375:131;2501:4;2375:131;:::i;:::-;2367:139;;2094:419;;;:::o;2519:180::-;2567:77;2564:1;2557:88;2664:4;2661:1;2654:15;2688:4;2685:1;2678:15;2705:194;2745:4;2765:20;2783:1;2765:20;:::i;:::-;2760:25;;2799:20;2817:1;2799:20;:::i;:::-;2794:25;;2843:1;2840;2836:9;2828:17;;2867:1;2861:4;2858:11;2855:37;;;2872:18;;:::i;:::-;2855:37;2705:194;;;;:::o;2905:191::-;2945:3;2964:20;2982:1;2964:20;:::i;:::-;2959:25;;2998:20;3016:1;2998:20;:::i;:::-;2993:25;;3041:1;3038;3034:9;3027:16;;3062:3;3059:1;3056:10;3053:36;;;3069:18;;:::i;:::-;3053:36;2905:191;;;;:::o"}, "methodIdentifiers": {"decrement(uint256)": "3a9ebefd", "get()": "6d4ce63c", "increment(uint256)": "7cf5dab0", "x()": "0c55699c"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"num\",\"type\":\"uint256\"}],\"name\":\"minusNumLog\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"num\",\"type\":\"uint256\"}],\"name\":\"plusNumLog\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"y\",\"type\":\"uint256\"}],\"name\":\"decrement\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"get\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"y\",\"type\":\"uint256\"}],\"name\":\"increment\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"x\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"project/contracts/Counter.sol\":\"Counter\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"project/contracts/Counter.sol\":{\"keccak256\":\"0x840d63e0ec9250e4764c8c8a6058757fc7a78984f8b81bcb54543eee74635780\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ec02646e02cc78e2c39e1cd8ecb3cdbb5ef52c0e9c437748fc0c21dae13b3c17\",\"dweb:/ipfs/QmWrrUAYYdhPxTAYg23vCSjwqKp1ZiZDYkzdiFJYEfiKVZ\"]}},\"version\":1}"}}}, "sources": {"project/contracts/Counter.sol": {"ast": {"absolutePath": "project/contracts/Counter.sol", "exportedSymbols": {"Counter": [62]}, "id": 63, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", "^", "0.8", ".18"], "nodeType": "PragmaDirective", "src": "32:24:0"}, {"abstract": false, "baseContracts": [], "canonicalName": "Counter", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "id": 62, "linearizedBaseContracts": [62], "name": "Counter", "nameLocation": "67:7:0", "nodeType": "ContractDefinition", "nodes": [{"constant": false, "functionSelector": "0c55699c", "id": 3, "mutability": "mutable", "name": "x", "nameLocation": "93:1:0", "nodeType": "VariableDeclaration", "scope": 62, "src": "81:13:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2, "name": "uint", "nodeType": "ElementaryTypeName", "src": "81:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "public"}, {"anonymous": false, "eventSelector": "0f15468f8ed097936d939a4f3fcd54599dad6662b341231150c1ac14e9119239", "id": 7, "name": "plusNumLog", "nameLocation": "107:10:0", "nodeType": "EventDefinition", "parameters": {"id": 6, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 5, "indexed": false, "mutability": "mutable", "name": "num", "nameLocation": "123:3:0", "nodeType": "VariableDeclaration", "scope": 7, "src": "118:8:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4, "name": "uint", "nodeType": "ElementaryTypeName", "src": "118:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "117:10:0"}, "src": "101:27:0"}, {"anonymous": false, "eventSelector": "4c5d8c5a3755953e6d51fbc8a808109f0efb2039c64de8d21fea4811a0126bf2", "id": 11, "name": "minusNumLog", "nameLocation": "139:11:0", "nodeType": "EventDefinition", "parameters": {"id": 10, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 9, "indexed": false, "mutability": "mutable", "name": "num", "nameLocation": "156:3:0", "nodeType": "VariableDeclaration", "scope": 11, "src": "151:8:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 8, "name": "uint", "nodeType": "ElementaryTypeName", "src": "151:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "150:10:0"}, "src": "133:28:0"}, {"body": {"id": 18, "nodeType": "Block", "src": "208:25:0", "statements": [{"expression": {"id": 16, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3, "src": "225:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 15, "id": 17, "nodeType": "Return", "src": "218:8:0"}]}, "functionSelector": "6d4ce63c", "id": 19, "implemented": true, "kind": "function", "modifiers": [], "name": "get", "nameLocation": "175:3:0", "nodeType": "FunctionDefinition", "parameters": {"id": 12, "nodeType": "ParameterList", "parameters": [], "src": "178:2:0"}, "returnParameters": {"id": 15, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 19, "src": "202:4:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 13, "name": "uint", "nodeType": "ElementaryTypeName", "src": "202:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "201:6:0"}, "scope": 62, "src": "166:67:0", "stateMutability": "view", "virtual": false, "visibility": "public"}, {"body": {"id": 39, "nodeType": "Block", "src": "273:93:0", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 27, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 25, "name": "y", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 21, "src": "291:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 26, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "295:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "291:5:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "696e76616c6964206e756d626572", "id": 28, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "298:16:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86", "typeString": "literal_string \"invalid number\""}, "value": "invalid number"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86", "typeString": "literal_string \"invalid number\""}], "id": 24, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "283:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 29, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "283:32:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 30, "nodeType": "ExpressionStatement", "src": "283:32:0"}, {"expression": {"id": 33, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 31, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3, "src": "325:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"id": 32, "name": "y", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 21, "src": "330:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "325:6:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 34, "nodeType": "ExpressionStatement", "src": "325:6:0"}, {"eventCall": {"arguments": [{"id": 36, "name": "y", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 21, "src": "357:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 35, "name": "plusNumLog", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7, "src": "346:10:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256)"}}, "id": 37, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "346:13:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 38, "nodeType": "EmitStatement", "src": "341:18:0"}]}, "functionSelector": "7cf5dab0", "id": 40, "implemented": true, "kind": "function", "modifiers": [], "name": "increment", "nameLocation": "248:9:0", "nodeType": "FunctionDefinition", "parameters": {"id": 22, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 21, "mutability": "mutable", "name": "y", "nameLocation": "263:1:0", "nodeType": "VariableDeclaration", "scope": 40, "src": "258:6:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 20, "name": "uint", "nodeType": "ElementaryTypeName", "src": "258:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "257:8:0"}, "returnParameters": {"id": 23, "nodeType": "ParameterList", "parameters": [], "src": "273:0:0"}, "scope": 62, "src": "239:127:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 60, "nodeType": "Block", "src": "405:94:0", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 48, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 46, "name": "y", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42, "src": "423:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 47, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "427:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "423:5:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "696e76616c6964206e756d626572", "id": 49, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "430:16:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86", "typeString": "literal_string \"invalid number\""}, "value": "invalid number"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_1bd7fc86f23d328edfd207430111127772dbf2b146cb075717f43888a828be86", "typeString": "literal_string \"invalid number\""}], "id": 45, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "415:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 50, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "415:32:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 51, "nodeType": "ExpressionStatement", "src": "415:32:0"}, {"expression": {"id": 54, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 52, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3, "src": "457:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "-=", "rightHandSide": {"id": 53, "name": "y", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42, "src": "462:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "457:6:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 55, "nodeType": "ExpressionStatement", "src": "457:6:0"}, {"eventCall": {"arguments": [{"id": 57, "name": "y", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42, "src": "490:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 56, "name": "minusNumLog", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11, "src": "478:11:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256)"}}, "id": 58, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "478:14:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 59, "nodeType": "EmitStatement", "src": "473:19:0"}]}, "functionSelector": "3a9ebefd", "id": 61, "implemented": true, "kind": "function", "modifiers": [], "name": "decrement", "nameLocation": "380:9:0", "nodeType": "FunctionDefinition", "parameters": {"id": 43, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42, "mutability": "mutable", "name": "y", "nameLocation": "395:1:0", "nodeType": "VariableDeclaration", "scope": 61, "src": "390:6:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41, "name": "uint", "nodeType": "ElementaryTypeName", "src": "390:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "389:8:0"}, "returnParameters": {"id": 44, "nodeType": "ParameterList", "parameters": [], "src": "405:0:0"}, "scope": 62, "src": "371:128:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}], "scope": 63, "src": "58:443:0", "usedErrors": [], "usedEvents": [7, 11]}], "src": "32:470:0"}, "id": 0}}}}