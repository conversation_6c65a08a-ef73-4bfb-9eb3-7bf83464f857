{"_format": "hh3-sol-build-info-1", "id": "solc-0_8_28-fb83ed9611c4c1fe2bb8b31a5847b4899eb763ab", "solcVersion": "0.8.28", "solcLongVersion": "0.8.28+commit.7893614a", "userSourceNameMap": {"contracts/Counter.sol": "project/contracts/Counter.sol"}, "input": {"language": "Solidity", "settings": {"evmVersion": "cancun", "outputSelection": {"*": {"": ["ast"], "*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "remappings": []}, "sources": {"project/contracts/Counter.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.18;\n\ncontract Counter {\n    uint public x;\n\n    event plusNumLog(uint num);\n    event minusNumLog(uint num);\n    function get() public view returns (uint) {\n        return x;\n    }\n\n    function increment(uint y) public {\n        require(y > 0, \"invalid number\");\n        x += y;\n        emit plusNumLog(y);\n    }\n    function decrement(uint y) public {\n        require(y > 0, \"invalid number\");\n        x -= y;\n        emit minusNumLog(y);\n    }\n}\n"}}}}