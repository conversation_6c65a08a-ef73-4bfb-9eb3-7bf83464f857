{"_format": "hh3-artifact-1", "contractName": "Counter", "sourceName": "contracts/Counter.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "num", "type": "uint256"}], "name": "minusNumLog", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "num", "type": "uint256"}], "name": "plusNumLog", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "y", "type": "uint256"}], "name": "decrement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "get", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "y", "type": "uint256"}], "name": "increment", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "x", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x6080604052348015600e575f5ffd5b506103c08061001c5f395ff3fe608060405234801561000f575f5ffd5b506004361061004a575f3560e01c80630c55699c1461004e5780633a9ebefd1461006c5780636d4ce63c146100885780637cf5dab0146100a6575b5f5ffd5b6100566100c2565b604051610063919061020d565b60405180910390f35b61008660048036038101906100819190610254565b6100c7565b005b61009061015a565b60405161009d919061020d565b60405180910390f35b6100c060048036038101906100bb9190610254565b610162565b005b5f5481565b5f8111610109576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610100906102d9565b60405180910390fd5b805f5f8282546101199190610324565b925050819055507f4c5d8c5a3755953e6d51fbc8a808109f0efb2039c64de8d21fea4811a0126bf28160405161014f919061020d565b60405180910390a150565b5f5f54905090565b5f81116101a4576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161019b906102d9565b60405180910390fd5b805f5f8282546101b49190610357565b925050819055507f0f15468f8ed097936d939a4f3fcd54599dad6662b341231150c1ac14e9119239816040516101ea919061020d565b60405180910390a150565b5f819050919050565b610207816101f5565b82525050565b5f6020820190506102205f8301846101fe565b92915050565b5f5ffd5b610233816101f5565b811461023d575f5ffd5b50565b5f8135905061024e8161022a565b92915050565b5f6020828403121561026957610268610226565b5b5f61027684828501610240565b91505092915050565b5f82825260208201905092915050565b7f696e76616c6964206e756d6265720000000000000000000000000000000000005f82015250565b5f6102c3600e8361027f565b91506102ce8261028f565b602082019050919050565b5f6020820190508181035f8301526102f0816102b7565b9050919050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52601160045260245ffd5b5f61032e826101f5565b9150610339836101f5565b9250828203905081811115610351576103506102f7565b5b92915050565b5f610361826101f5565b915061036c836101f5565b9250828201905080821115610384576103836102f7565b5b9291505056fea26469706673582212206904ac55654c31a4ab337443f1fb26969d3d734f5c3e16598a4f511d330f379964736f6c634300081c0033", "deployedBytecode": "0x608060405234801561000f575f5ffd5b506004361061004a575f3560e01c80630c55699c1461004e5780633a9ebefd1461006c5780636d4ce63c146100885780637cf5dab0146100a6575b5f5ffd5b6100566100c2565b604051610063919061020d565b60405180910390f35b61008660048036038101906100819190610254565b6100c7565b005b61009061015a565b60405161009d919061020d565b60405180910390f35b6100c060048036038101906100bb9190610254565b610162565b005b5f5481565b5f8111610109576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610100906102d9565b60405180910390fd5b805f5f8282546101199190610324565b925050819055507f4c5d8c5a3755953e6d51fbc8a808109f0efb2039c64de8d21fea4811a0126bf28160405161014f919061020d565b60405180910390a150565b5f5f54905090565b5f81116101a4576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161019b906102d9565b60405180910390fd5b805f5f8282546101b49190610357565b925050819055507f0f15468f8ed097936d939a4f3fcd54599dad6662b341231150c1ac14e9119239816040516101ea919061020d565b60405180910390a150565b5f819050919050565b610207816101f5565b82525050565b5f6020820190506102205f8301846101fe565b92915050565b5f5ffd5b610233816101f5565b811461023d575f5ffd5b50565b5f8135905061024e8161022a565b92915050565b5f6020828403121561026957610268610226565b5b5f61027684828501610240565b91505092915050565b5f82825260208201905092915050565b7f696e76616c6964206e756d6265720000000000000000000000000000000000005f82015250565b5f6102c3600e8361027f565b91506102ce8261028f565b602082019050919050565b5f6020820190508181035f8301526102f0816102b7565b9050919050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52601160045260245ffd5b5f61032e826101f5565b9150610339836101f5565b9250828203905081811115610351576103506102f7565b5b92915050565b5f610361826101f5565b915061036c836101f5565b9250828201905080821115610384576103836102f7565b5b9291505056fea26469706673582212206904ac55654c31a4ab337443f1fb26969d3d734f5c3e16598a4f511d330f379964736f6c634300081c0033", "linkReferences": {}, "deployedLinkReferences": {}, "immutableReferences": {}, "inputSourceName": "project/contracts/Counter.sol", "buildInfoId": "solc-0_8_28-fb83ed9611c4c1fe2bb8b31a5847b4899eb763ab"}