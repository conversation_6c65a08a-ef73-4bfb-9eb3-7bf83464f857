<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo Contract Tester</title>
    <script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        input, button, textarea {
            padding: 10px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .task {
            background: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            color: green;
            background: #e6ffe6;
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Todo Contract Tester</h1>
        
        <div class="section">
            <h3>连接设置</h3>
            <input type="text" id="contractAddress" placeholder="合约地址" style="width: 400px;">
            <button onclick="connectContract()">连接合约</button>
            <button onclick="connectWallet()">连接钱包</button>
            <div id="connectionStatus"></div>
        </div>

        <div class="section">
            <h3>创建任务</h3>
            <input type="text" id="taskText" placeholder="任务描述" style="width: 300px;">
            <button onclick="createTask()">创建任务</button>
        </div>

        <div class="section">
            <h3>删除任务</h3>
            <input type="number" id="taskIndex" placeholder="任务索引" min="0">
            <button onclick="removeTask()">删除任务</button>
        </div>

        <div class="section">
            <h3>查看任务</h3>
            <button onclick="loadTasks()">刷新任务列表</button>
            <div id="tasksList"></div>
        </div>

        <div id="messages"></div>
    </div>

    <script>
        let provider;
        let signer;
        let contract;
        
        const contractABI = [
            "function createTask(string calldata _text) external",
            "function removeTask(uint idx) external",
            "function todos(uint) public view returns (uint8 status, string text, uint timestamp)"
        ];

        async function connectWallet() {
            try {
                if (typeof window.ethereum !== 'undefined') {
                    await window.ethereum.request({ method: 'eth_requestAccounts' });
                    provider = new ethers.providers.Web3Provider(window.ethereum);
                    signer = provider.getSigner();
                    const address = await signer.getAddress();
                    showMessage(`钱包已连接: ${address}`, 'success');
                } else {
                    showMessage('请安装 MetaMask!', 'error');
                }
            } catch (error) {
                showMessage(`连接钱包失败: ${error.message}`, 'error');
            }
        }

        async function connectContract() {
            try {
                const address = document.getElementById('contractAddress').value;
                if (!address) {
                    showMessage('请输入合约地址', 'error');
                    return;
                }
                if (!signer) {
                    showMessage('请先连接钱包', 'error');
                    return;
                }
                
                contract = new ethers.Contract(address, contractABI, signer);
                showMessage(`合约已连接: ${address}`, 'success');
                loadTasks();
            } catch (error) {
                showMessage(`连接合约失败: ${error.message}`, 'error');
            }
        }

        async function createTask() {
            try {
                const text = document.getElementById('taskText').value;
                if (!text) {
                    showMessage('请输入任务描述', 'error');
                    return;
                }
                if (!contract) {
                    showMessage('请先连接合约', 'error');
                    return;
                }

                showMessage('正在创建任务...', 'success');
                const tx = await contract.createTask(text);
                await tx.wait();
                showMessage('任务创建成功!', 'success');
                document.getElementById('taskText').value = '';
                loadTasks();
            } catch (error) {
                showMessage(`创建任务失败: ${error.message}`, 'error');
            }
        }

        async function removeTask() {
            try {
                const index = document.getElementById('taskIndex').value;
                if (index === '') {
                    showMessage('请输入任务索引', 'error');
                    return;
                }
                if (!contract) {
                    showMessage('请先连接合约', 'error');
                    return;
                }

                showMessage('正在删除任务...', 'success');
                const tx = await contract.removeTask(index);
                await tx.wait();
                showMessage('任务删除成功!', 'success');
                document.getElementById('taskIndex').value = '';
                loadTasks();
            } catch (error) {
                showMessage(`删除任务失败: ${error.message}`, 'error');
            }
        }

        async function loadTasks() {
            try {
                if (!contract) {
                    showMessage('请先连接合约', 'error');
                    return;
                }

                const tasksList = document.getElementById('tasksList');
                tasksList.innerHTML = '<p>正在加载任务...</p>';

                let tasks = [];
                let index = 0;
                
                // 尝试读取任务，直到遇到错误（表示没有更多任务）
                while (true) {
                    try {
                        const task = await contract.todos(index);
                        if (task.text === '') break; // 空任务表示结束
                        
                        tasks.push({
                            index: index,
                            status: ['Pending', 'Processing', 'Completed', 'Canceled'][task.status],
                            text: task.text,
                            timestamp: new Date(task.timestamp * 1000).toLocaleString()
                        });
                        index++;
                    } catch (error) {
                        break; // 没有更多任务
                    }
                }

                if (tasks.length === 0) {
                    tasksList.innerHTML = '<p>暂无任务</p>';
                } else {
                    tasksList.innerHTML = tasks.map(task => `
                        <div class="task">
                            <strong>任务 ${task.index}:</strong> ${task.text}<br>
                            <small>状态: ${task.status} | 创建时间: ${task.timestamp}</small>
                        </div>
                    `).join('');
                }
            } catch (error) {
                showMessage(`加载任务失败: ${error.message}`, 'error');
            }
        }

        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            messagesDiv.appendChild(messageDiv);
            
            // 3秒后自动删除消息
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // 页面加载时的提示
        window.onload = function() {
            showMessage('请先连接钱包，然后输入合约地址进行连接', 'success');
        };
    </script>
</body>
</html>
