{
    "[solidity]": {
        "editor.quickSuggestions": {
            "comments": "off",
            "strings": "off",
            "other": "off"
        },
        "editor.suggestOnTriggerCharacters": false,
        "editor.acceptSuggestionOnCommitCharacter": false,
        "editor.acceptSuggestionOnEnter": "off",
        "editor.tabCompletion": "off",
        "editor.wordBasedSuggestions": "off",
        "editor.parameterHints.enabled": false,
        "editor.hover.enabled": false,
        "editor.suggest.showKeywords": false,
        "editor.suggest.showSnippets": false,
        "editor.suggest.showFunctions": false,
        "editor.suggest.showConstructors": false,
        "editor.suggest.showMethods": false,
        "editor.suggest.showProperties": false,
        "editor.suggest.showVariables": false,
        "editor.suggest.showClasses": false,
        "editor.suggest.showInterfaces": false,
        "editor.suggest.showModules": false,
        "editor.suggest.showWords": false,
        "editor.suggest.showReferences": false,
        "editor.inlineSuggest.enabled": false,
        "editor.suggest.insertMode": "replace",
        "typescript.suggest.enabled": false,
        "javascript.suggest.enabled": false
    },
}