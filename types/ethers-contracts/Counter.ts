/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type { BaseContract, BigN<PERSON>berish, BytesLike, FunctionFragment, Result, Interface, EventFragment, ContractRunner, ContractMethod, Listener } from "ethers"
import type { TypedContractEvent, TypedDeferredTopicFilter, TypedEventLog, TypedLogDescription, TypedListener, TypedContractMethod } from "./common.js"
  

  export interface CounterInterface extends Interface {
    getFunction(nameOrSignature: "decrement" | "get" | "increment" | "x"): FunctionFragment;

    getEvent(nameOrSignatureOrTopic: "minusNumLog" | "plusNumLog"): EventFragment;

    encodeFunctionData(functionFragment: 'decrement', values: [BigNumberish]): string;
encodeFunctionData(functionFragment: 'get', values?: undefined): string;
encodeFunctionData(functionFragment: 'increment', values: [BigNumberish]): string;
encodeFunctionData(functionFragment: 'x', values?: undefined): string;

    decodeFunctionResult(functionFragment: 'decrement', data: BytesLike): Result;
decodeFunctionResult(functionFragment: 'get', data: BytesLike): Result;
decodeFunctionResult(functionFragment: 'increment', data: BytesLike): Result;
decodeFunctionResult(functionFragment: 'x', data: BytesLike): Result;
  }

  
    export namespace minusNumLogEvent {
      export type InputTuple = [num: BigNumberish];
      export type OutputTuple = [num: bigint];
      export interface OutputObject {num: bigint };
      export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
      export type Filter = TypedDeferredTopicFilter<Event>
      export type Log = TypedEventLog<Event>
      export type LogDescription = TypedLogDescription<Event>
    }

  

    export namespace plusNumLogEvent {
      export type InputTuple = [num: BigNumberish];
      export type OutputTuple = [num: bigint];
      export interface OutputObject {num: bigint };
      export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>
      export type Filter = TypedDeferredTopicFilter<Event>
      export type Log = TypedEventLog<Event>
      export type LogDescription = TypedLogDescription<Event>
    }

  

  export interface Counter extends BaseContract {
    
    connect(runner?: ContractRunner | null): Counter;
    waitForDeployment(): Promise<this>;

    interface: CounterInterface;

    
  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined,
  ): Promise<Array<TypedEventLog<TCEvent>>>
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(event: TCEvent, listener: TypedListener<TCEvent>): Promise<this>
  on<TCEvent extends TypedContractEvent>(filter: TypedDeferredTopicFilter<TCEvent>, listener: TypedListener<TCEvent>): Promise<this>
  
  once<TCEvent extends TypedContractEvent>(event: TCEvent, listener: TypedListener<TCEvent>): Promise<this>
  once<TCEvent extends TypedContractEvent>(filter: TypedDeferredTopicFilter<TCEvent>, listener: TypedListener<TCEvent>): Promise<this>

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>
  removeAllListeners<TCEvent extends TypedContractEvent>(event?: TCEvent): Promise<this>


    
    
    decrement: TypedContractMethod<
      [y: BigNumberish, ],
      [void],
      'nonpayable'
    >
    

    
    get: TypedContractMethod<
      [],
      [bigint],
      'view'
    >
    

    
    increment: TypedContractMethod<
      [y: BigNumberish, ],
      [void],
      'nonpayable'
    >
    

    
    x: TypedContractMethod<
      [],
      [bigint],
      'view'
    >
    


    getFunction<T extends ContractMethod = ContractMethod>(key: string | FunctionFragment): T;

    getFunction(nameOrSignature: 'decrement'): TypedContractMethod<
      [y: BigNumberish, ],
      [void],
      'nonpayable'
    >;
getFunction(nameOrSignature: 'get'): TypedContractMethod<
      [],
      [bigint],
      'view'
    >;
getFunction(nameOrSignature: 'increment'): TypedContractMethod<
      [y: BigNumberish, ],
      [void],
      'nonpayable'
    >;
getFunction(nameOrSignature: 'x'): TypedContractMethod<
      [],
      [bigint],
      'view'
    >;

    getEvent(key: 'minusNumLog'): TypedContractEvent<minusNumLogEvent.InputTuple, minusNumLogEvent.OutputTuple, minusNumLogEvent.OutputObject>;
getEvent(key: 'plusNumLog'): TypedContractEvent<plusNumLogEvent.InputTuple, plusNumLogEvent.OutputTuple, plusNumLogEvent.OutputObject>;

    filters: {
      
      'minusNumLog(uint256)': TypedContractEvent<minusNumLogEvent.InputTuple, minusNumLogEvent.OutputTuple, minusNumLogEvent.OutputObject>;
      minusNumLog: TypedContractEvent<minusNumLogEvent.InputTuple, minusNumLogEvent.OutputTuple, minusNumLogEvent.OutputObject>;
    

      'plusNumLog(uint256)': TypedContractEvent<plusNumLogEvent.InputTuple, plusNumLogEvent.OutputTuple, plusNumLogEvent.OutputObject>;
      plusNumLog: TypedContractEvent<plusNumLogEvent.InputTuple, plusNumLogEvent.OutputTuple, plusNumLogEvent.OutputObject>;
    
    };
  }