import { ethers } from "hardhat";

async function main() {
  console.log("Deploying Todo contract...");

  // 部署合约
  const Todo = await ethers.getContractFactory("Todo");
  const todo = await Todo.deploy();
  await todo.waitForDeployment();

  const address = await todo.getAddress();
  console.log("Todo deployed to:", address);

  // 测试合约功能
  console.log("\n=== Testing Todo Contract ===");

  // 创建任务
  console.log("Creating tasks...");
  await todo.createTask("学习 Solidity");
  await todo.createTask("完成 Hardhat 部署");
  await todo.createTask("测试智能合约");

  // 查看任务
  console.log("\nTasks created:");
  for (let i = 0; i < 3; i++) {
    const task = await todo.todos(i);
    console.log(`Task ${i}:`, {
      status: task.status,
      text: task.text,
      timestamp: new Date(Number(task.timestamp) * 1000).toLocaleString()
    });
  }

  // 删除一个任务
  console.log("\nRemoving task 1...");
  await todo.removeTask(1);

  // 查看剩余任务
  console.log("\nRemaining tasks:");
  try {
    for (let i = 0; i < 2; i++) {
      const task = await todo.todos(i);
      console.log(`Task ${i}:`, {
        status: task.status,
        text: task.text,
        timestamp: new Date(Number(task.timestamp) * 1000).toLocaleString()
      });
    }
  } catch (error) {
    console.log("No more tasks or error:", error.message);
  }

  console.log("\n=== Contract Address ===");
  console.log("You can interact with the contract at:", address);
  console.log("Use 'npx hardhat console' to interact manually");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
