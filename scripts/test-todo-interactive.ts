import { ethers } from "hardhat";
import * as readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

let todo: any;

async function main() {
  console.log("🚀 部署 Todo 合约...");
  
  // 部署合约
  const Todo = await ethers.getContractFactory("Todo");
  todo = await Todo.deploy();
  await todo.waitForDeployment();

  const address = await todo.getAddress();
  console.log("✅ Todo 合约已部署到:", address);
  console.log("\n=== Todo 合约测试界面 ===");
  
  showMenu();
}

function showMenu() {
  console.log("\n请选择操作:");
  console.log("1. 创建任务");
  console.log("2. 查看所有任务");
  console.log("3. 删除任务");
  console.log("4. 查看特定任务");
  console.log("5. 退出");
  
  rl.question("请输入选项 (1-5): ", handleMenuChoice);
}

async function handleMenuChoice(choice: string) {
  try {
    switch (choice.trim()) {
      case '1':
        await createTask();
        break;
      case '2':
        await viewAllTasks();
        break;
      case '3':
        await deleteTask();
        break;
      case '4':
        await viewSpecificTask();
        break;
      case '5':
        console.log("👋 再见!");
        rl.close();
        process.exit(0);
        break;
      default:
        console.log("❌ 无效选项，请重新选择");
        showMenu();
    }
  } catch (error) {
    console.error("❌ 操作失败:", error.message);
    showMenu();
  }
}

async function createTask() {
  rl.question("请输入任务描述: ", async (text) => {
    try {
      console.log("⏳ 正在创建任务...");
      const tx = await todo.createTask(text);
      await tx.wait();
      console.log("✅ 任务创建成功!");
      showMenu();
    } catch (error) {
      console.error("❌ 创建任务失败:", error.message);
      showMenu();
    }
  });
}

async function viewAllTasks() {
  console.log("\n📋 所有任务:");
  console.log("=" .repeat(50));
  
  let index = 0;
  let hasTask = true;
  
  while (hasTask) {
    try {
      const task = await todo.todos(index);
      if (task.text === '') {
        hasTask = false;
      } else {
        const statusNames = ['待处理', '进行中', '已完成', '已取消'];
        const date = new Date(Number(task.timestamp) * 1000);
        
        console.log(`任务 ${index}:`);
        console.log(`  📝 描述: ${task.text}`);
        console.log(`  📊 状态: ${statusNames[task.status]}`);
        console.log(`  📅 创建时间: ${date.toLocaleString()}`);
        console.log("-".repeat(30));
        index++;
      }
    } catch (error) {
      hasTask = false;
    }
  }
  
  if (index === 0) {
    console.log("📭 暂无任务");
  }
  
  showMenu();
}

async function deleteTask() {
  rl.question("请输入要删除的任务索引: ", async (indexStr) => {
    try {
      const index = parseInt(indexStr);
      if (isNaN(index) || index < 0) {
        console.log("❌ 请输入有效的任务索引");
        showMenu();
        return;
      }
      
      console.log("⏳ 正在删除任务...");
      const tx = await todo.removeTask(index);
      await tx.wait();
      console.log("✅ 任务删除成功!");
      showMenu();
    } catch (error) {
      console.error("❌ 删除任务失败:", error.message);
      showMenu();
    }
  });
}

async function viewSpecificTask() {
  rl.question("请输入任务索引: ", async (indexStr) => {
    try {
      const index = parseInt(indexStr);
      if (isNaN(index) || index < 0) {
        console.log("❌ 请输入有效的任务索引");
        showMenu();
        return;
      }
      
      const task = await todo.todos(index);
      if (task.text === '') {
        console.log("❌ 任务不存在");
      } else {
        const statusNames = ['待处理', '进行中', '已完成', '已取消'];
        const date = new Date(Number(task.timestamp) * 1000);
        
        console.log(`\n📋 任务 ${index} 详情:`);
        console.log(`📝 描述: ${task.text}`);
        console.log(`📊 状态: ${statusNames[task.status]}`);
        console.log(`📅 创建时间: ${date.toLocaleString()}`);
      }
      showMenu();
    } catch (error) {
      console.error("❌ 查看任务失败:", error.message);
      showMenu();
    }
  });
}

main().catch((error) => {
  console.error(error);
  process.exit(1);
});
