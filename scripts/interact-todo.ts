import { ethers } from "hardhat";

async function main() {
  // 部署合约
  console.log("Deploying Todo contract...");
  const Todo = await ethers.getContractFactory("Todo");
  const todo = await Todo.deploy();
  await todo.waitForDeployment();

  const address = await todo.getAddress();
  console.log("Todo deployed to:", address);

  // 提供交互式命令示例
  console.log("\n=== 交互式命令示例 ===");
  console.log("现在你可以在 Hardhat 控制台中使用以下命令：");
  console.log("npx hardhat console");
  console.log("");
  console.log("// 连接到已部署的合约");
  console.log(`const todo = await ethers.getContractAt("Todo", "${address}");`);
  console.log("");
  console.log("// 创建任务");
  console.log('await todo.createTask("我的第一个任务");');
  console.log('await todo.createTask("学习区块链");');
  console.log("");
  console.log("// 查看任务");
  console.log("await todo.todos(0);");
  console.log("await todo.todos(1);");
  console.log("");
  console.log("// 删除任务");
  console.log("await todo.removeTask(0);");
  console.log("");
  console.log("// 查看剩余任务");
  console.log("await todo.todos(0);");

  // 保存合约地址到文件
  const fs = require('fs');
  const contractInfo = {
    address: address,
    deployedAt: new Date().toISOString()
  };
  fs.writeFileSync('deployed-contracts.json', JSON.stringify(contractInfo, null, 2));
  console.log("\n合约地址已保存到 deployed-contracts.json");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
